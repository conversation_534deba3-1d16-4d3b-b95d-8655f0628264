# 收据抽屉组件优化

## 概述
对 `src/views/financeManage/components/receiptDrawer.vue` 组件进行了优化，在查看和下载按钮点击时先调用 `getReceiptDetail` 接口获取详细信息和地址。

## 主要修改内容

### 1. 接口导入
```typescript
// 新增导入
import { getReceiptDetail, type ReceiptDetailDTO } from '@/api/receipt'
```

### 2. 查看功能优化
**原逻辑**：
- 直接使用列表数据中的 `viewUrl` 字段
- 如果没有则提示"暂无查看链接"

**新逻辑**：
```typescript
const handleView = async (record: any) => {
  if (!record.id) {
    Message.warning('缺少收据ID')
    return
  }

  try {
    const params: ReceiptDetailDTO = { id: record.id }
    const response = await getReceiptDetail(params)
    
    if (response && response.data) {
      const detail = response.data
      if (detail.viewUrl) {
        window.open(detail.viewUrl, '_blank')
      } else {
        Message.warning('暂无查看链接')
      }
    } else {
      Message.warning('获取收据详情失败')
    }
  } catch (error) {
    console.error('获取收据详情失败:', error)
    Message.error('获取收据详情失败')
  }
}
```

### 3. 下载功能优化
**原逻辑**：
- 直接使用列表数据中的 `downloadUrl` 字段
- 如果没有则提示"暂无下载链接"

**新逻辑**：
```typescript
const handleDownload = async (record: any) => {
  if (!record.id) {
    Message.warning('缺少收据ID')
    return
  }

  try {
    const params: ReceiptDetailDTO = { id: record.id }
    const response = await getReceiptDetail(params)
    
    if (response && response.data) {
      const detail = response.data
      if (detail.downloadUrl) {
        // 创建a标签下载
        const link = document.createElement('a')
        link.href = detail.downloadUrl
        link.target = '_blank'
        link.rel = 'noopener noreferrer'
        link.setAttribute('download', `收据_${record.serialNumber || record.id}.pdf`)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        Message.success('下载成功')
      } else {
        Message.warning('暂无下载链接')
      }
    } else {
      Message.warning('获取收据详情失败')
    }
  } catch (error) {
    console.error('获取收据详情失败:', error)
    Message.error('获取收据详情失败')
  }
}
```

## 技术要点

### 1. 异步处理
- 将查看和下载方法改为 `async` 函数
- 使用 `await` 等待接口响应
- 添加了完整的错误处理机制

### 2. 参数验证
- 在调用接口前验证 `record.id` 是否存在
- 如果缺少ID则提示用户并返回

### 3. 响应处理
- 检查接口响应的有效性 (`response && response.data`)
- 从详情数据中获取 `viewUrl` 和 `downloadUrl`
- 根据字段存在性决定是否执行操作

### 4. 错误处理
- 使用 try-catch 捕获接口调用异常
- 提供用户友好的错误提示
- 在控制台输出详细错误信息便于调试

## 接口依赖

### getReceiptDetail 接口
- **路径**: `/business-rent-admin/receipt/detail`
- **方法**: GET
- **参数**: `{ id: string }` - 收据ID
- **返回**: `AjaxResult<ReceiptVo>` - 包含收据详细信息

### 预期返回字段
组件期望详情接口返回的数据中包含以下字段：
- `viewUrl`: 查看链接地址
- `downloadUrl`: 下载链接地址

## 优势

1. **数据实时性**: 每次操作都获取最新的详情数据
2. **安全性**: 避免使用可能过期的列表数据中的链接
3. **灵活性**: 支持动态生成的查看和下载链接
4. **用户体验**: 提供清晰的错误提示和成功反馈
5. **可维护性**: 统一的错误处理和日志记录

## 注意事项

1. **字段名称**: 代码中假设详情接口返回 `viewUrl` 和 `downloadUrl` 字段，如果实际字段名不同需要调整
2. **性能考虑**: 每次查看/下载都会调用详情接口，如果频繁操作可考虑缓存机制
3. **网络异常**: 已添加完整的异常处理，确保网络问题不会影响用户体验
4. **兼容性**: 保持了原有的下载文件命名逻辑和用户提示
