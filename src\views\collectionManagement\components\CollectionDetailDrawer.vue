<template>
    <a-drawer
        v-model:visible="visible"
        :title="drawerTitle"
        class="common-drawer"
        :footer="true"
        @cancel="handleCancel"
    >
        <template #footer>
            <a-space>
                <a-button @click="handleCancel">关闭</a-button>
                <a-button type="primary" @click="handleDownload">{{ downloadButtonText }}</a-button>
            </a-space>
        </template>

        <div v-if="detailData" class="detail-content">
            <!-- 基本信息 -->
            <div class="detail-section">
                <SectionTitle title="基本信息" style="margin-bottom: 16px;" />
                <a-form
                    :model="detailData"
                    label-align="right"
                    :label-col-props="{ span: 6 }"
                    :wrapper-col-props="{ span: 18 }"
                    auto-label-width
                >
                    <a-row :gutter="16">
                        <a-col :span="8">
                            <a-form-item label="项目">
                                <a-input v-model="detailData.projectName" disabled />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item label="合同号">
                                <a-input v-model="detailData.contractNo" disabled />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item label="承租方">
                                <a-input v-model="detailData.customerName" disabled />
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="16">
                        <a-col :span="8">
                            <a-form-item label="租期">
                                <a-input :model-value="detailData?.startDate && detailData?.endDate ? `${detailData.startDate} 至 ${detailData.endDate}` : '-'" disabled />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item label="租赁房源数">
                                <a-input v-model="detailData.roomCount" disabled >
                                    <template #append>
                                        间
                                    </template>
                                </a-input>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item label="租赁房源">
                                <a-input v-model="detailData.roomName" disabled />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>

            <!-- 账单信息 -->
            <div class="detail-section">
                <SectionTitle :title="billSectionTitle" style="margin-bottom: 16px;" />
                <a-table
                    :columns="billColumns"
                    :data="detailData?.moneyList || []"
                    :pagination="false"
                    :bordered="{ cell: true }"
                    :stripe="true"
                    size="small"
                >
                    <template #totalAmount="{ record }">
                        {{ formatAmount(record.totalAmount) }}
                    </template>
                    <template #discountAmount="{ record }">
                        {{ formatAmount(record.discountAmount) }}
                    </template>
                    <template #actualReceivable="{ record }">
                        {{ formatAmount(record.actualReceivable) }}
                    </template>
                    <template #receivedAmount="{ record }">
                        {{ formatAmount(record.receivedAmount) }}
                    </template>
                    <template #unreceivedAmount="{ record }">
                        {{ formatAmount(record.unreceivedAmount) }}
                    </template>
                </a-table>
            </div>

            <!-- 当前催缴情况 -->
            <div class="detail-section">
                <SectionTitle title="当前催缴情况" style="margin-bottom: 16px;" />
                <a-form
                    :model="detailData"
                    label-align="right"
                    :label-col-props="{ span: 6 }"
                    :wrapper-col-props="{ span: 18 }"
                    auto-label-width
                >
                    <a-row :gutter="16">
                        <a-col :span="8">
                            <a-form-item label="催款金额">
                                <a-input-number
                                    :model-value="detailData?.totalMoney"
                                    :precision="2"
                                    disabled
                                    style="width: 100%"
                                >
                                    <template #append>元</template>
                                </a-input-number>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item label="当前已缴金额">
                                <a-input-number
                                    :model-value="detailData?.lastReceivedMoney"
                                    :precision="2"
                                    disabled
                                    style="width: 100%"
                                >
                                    <template #append>元</template>
                                </a-input-number>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item label="当前未缴金额">
                                <a-input-number
                                    :model-value="currentUnpaidAmount"
                                    :precision="2"
                                    disabled
                                    style="width: 100%"
                                >
                                    <template #append>元</template>
                                </a-input-number>
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="16">
                        <a-col :span="8">
                            <a-form-item label="催缴单发送时间">
                                <a-input v-model="detailData.sendTime" disabled />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item label="催缴单接收人">
                                <a-input v-model="detailData.receivePerson" disabled />
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item label="接收人手机号">
                                <a-input v-model="detailData.receivePhone" disabled />
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="16">
                        <a-col :span="8">
                            <a-form-item label="接收人查看状态">
                                <a-input :model-value="detailData?.viewStatus === 1 ? '已查看' : '未查看'" disabled />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
        </div>
    </a-drawer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Message } from '@arco-design/web-vue'
import { getContractBillDetail, downloadContractBill, CostType, type ContractBillVo, type ContractBillDetailVo } from '@/api/collectionManagement'
import SectionTitle from '@/components/sectionTitle/index.vue'



// 抽屉显示状态
const visible = ref(false)
const detailData = ref<ContractBillDetailVo>()
const currentRecord = ref<ContractBillVo>()

// 费用类型映射
const costTypeMap: Record<number, string> = {
    [CostType.DEPOSIT]: '保证金',
    [CostType.RENT]: '租金',
    [CostType.OTHER]: '其他费用'
}

// 账单表格列配置
const billColumns = [
    {
        title: '账单类型',
        dataIndex: 'subjectName',
        align: 'center',
        width: 100,
    },
    {
        title: '账单周期',
        dataIndex: 'startDate',
        align: 'center',
        width: 160,
        render: ({ record }: any) => {
            if (record.startDate && record.endDate) {
                return `${record.startDate} 至 ${record.endDate}`
            }
            return '-'
        }
    },
    {
        title: '应付日期',
        dataIndex: 'receivableDate',
        align: 'center',
        width: 100
    },
    {
        title: '账单总额',
        dataIndex: 'totalAmount',
        slotName: 'totalAmount',
        align: 'center',
        width: 100
    },
    {
        title: '免租优惠',
        dataIndex: 'discountAmount',
        slotName: 'discountAmount',
        align: 'center',
        width: 100
    },
    {
        title: '应付金额',
        dataIndex: 'actualReceivable',
        slotName: 'actualReceivable',
        align: 'center',
        width: 100
    },
    {
        title: '已付金额',
        dataIndex: 'receivedAmount',
        slotName: 'receivedAmount',
        align: 'center',
        width: 100
    },
    {
        title: '未付金额',
        dataIndex: 'unreceivedAmount',
        slotName: 'unreceivedAmount',
        align: 'center',
        width: 100
    }
]

// 动态标题和文本
const drawerTitle = computed(() => {
    return detailData.value?.type === 2 ? '缴款通知单' : '催缴函'
})

const downloadButtonText = computed(() => {
    return detailData.value?.type === 2 ? '下载缴款通知单' : '下载催缴函'
})

// 账单标题
const billSectionTitle = computed(() => {
    return detailData.value?.type === 2 ? '本期账单' : '逾期账单'
})

// 计算当前未缴金额（备用方案，优先使用后端提供的字段）
const currentUnpaidAmount = computed(() => {
    if (!detailData.value) return 0

    // 优先使用后端提供的字段
    if (detailData.value.currentUnreceivedMoney !== undefined) {
        return detailData.value.currentUnreceivedMoney
    }

    // 备用计算：催款金额 - 当前已缴金额
    const totalMoney = detailData.value.totalMoney || 0
    const lastReceivedMoney = detailData.value.lastReceivedMoney || 0

    // 使用高精度计算避免浮点数问题
    return Math.round((totalMoney - lastReceivedMoney) * 100) / 100
})



// 显示抽屉
const show = async (record: ContractBillVo) => {
    currentRecord.value = record
    visible.value = true

    try {
        const response = await getContractBillDetail(record.id!)
        detailData.value = response.data || response
    } catch (error) {
        console.error('获取催缴详情失败:', error)
    }
}

// 关闭抽屉
const handleCancel = () => {
    visible.value = false
    detailData.value = undefined
    currentRecord.value = undefined
}

// 下载催缴函
const handleDownload = async () => {
    if (!detailData.value?.id) return

    try {
        const response = await downloadContractBill(detailData.value.id)
        if (response.code === 200 && response.msg) {
            // 创建a标签下载
            const link = document.createElement('a')
            link.href = response.msg
            link.target = '_blank'
            link.rel = 'noopener noreferrer'
            // 尝试设置下载属性
            link.setAttribute('download', `催缴通知单_${currentRecord.value?.contractNo || detailData.value.id}.pdf`)
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            Message.success('下载成功')
        }
    } catch (error) {
        console.error('下载失败:', error)
    }
}



// 格式化金额
const formatAmount = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return '-'
    return amount.toFixed(2)
}

// 暴露方法
defineExpose({
    show
})
</script>

<style scoped lang="less">
.detail-content {
    .detail-section {
        margin-bottom: 24px;
        
        .section-subtitle {
            font-size: 12px;
            color: #86909c;
        }
        
        .total-info {
            margin-top: 16px;
            text-align: right;
            font-size: 14px;
            color: #1d2129;
        }
    }
}
</style>
